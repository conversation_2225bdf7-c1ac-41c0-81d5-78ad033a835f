<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Naroop - Connect & Share</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f8f6f3 0%, #ffffff 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 100vw;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
        }

        /* Header */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 20px 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 32px;
            font-weight: 700;
            background: linear-gradient(45deg, #8B4513, #D2691E);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-buttons {
            display: flex;
            gap: 15px;
        }

        .nav-btn {
            background: transparent;
            border: 2px solid #8B4513;
            color: #8B4513;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            background: #8B4513;
            color: white;
            transform: translateY(-2px);
        }

        .nav-btn.primary {
            background: #8B4513;
            color: white;
        }

        .nav-btn.primary:hover {
            background: #7a3c0f;
        }

        /* Main Content */
        .main-content {
            display: grid;
            grid-template-columns: 1fr 2fr 1fr;
            gap: 30px;
            align-items: start;
            min-height: calc(100vh - 140px);
        }

        .sidebar {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .sidebar h3 {
            margin-bottom: 20px;
            color: #333;
            font-size: 18px;
        }

        .sidebar-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            cursor: pointer;
            transition: all 0.3s ease;
            border-radius: 10px;
            padding: 12px 15px;
            margin-bottom: 8px;
        }

        .sidebar-item:hover {
            background: rgba(139, 69, 19, 0.1);
            transform: translateX(5px);
        }

        .sidebar-item.active {
            background: rgba(139, 69, 19, 0.15);
            color: #8B4513;
            font-weight: 600;
        }

        .sidebar-item::before {
            content: "📱";
            margin-right: 12px;
            font-size: 16px;
        }

        .sidebar-item:nth-child(2)::before { content: "🏠"; }
        .sidebar-item:nth-child(3)::before { content: "🔍"; }
        .sidebar-item:nth-child(4)::before { content: "💬"; }
        .sidebar-item:nth-child(5)::before { content: "👤"; }

        /* Feed Section */
        .feed-section {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .feed-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .feed-title {
            font-size: 24px;
            font-weight: 700;
            color: #333;
        }

        .refresh-btn {
            background: linear-gradient(45deg, #8B4513, #D2691E);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(139, 69, 19, 0.3);
        }

        .refresh-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(139, 69, 19, 0.4);
        }

        .create-post {
            background: rgba(201, 168, 118, 0.1);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            border: 2px dashed rgba(201, 168, 118, 0.3);
            text-align: center;
        }

        .create-post-icon {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.6;
        }

        .create-post h3 {
            color: #c9a876;
            margin-bottom: 10px;
            font-size: 20px;
        }

        .create-post p {
            color: #666;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .create-btn {
            background: linear-gradient(45deg, #c9a876, #f4e4bc);
            color: #1a1a1a;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
        }

        .create-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(201, 168, 118, 0.4);
        }

        .load-more {
            background: rgba(139, 69, 19, 0.05);
            border: 2px solid #8B4513;
            color: #8B4513;
            padding: 15px 40px;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 30px;
            width: 100%;
        }

        .load-more:hover {
            background: #8B4513;
            color: white;
            transform: translateY(-2px);
        }

        /* Right Sidebar */
        .trending {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .trending-item {
            padding: 15px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .trending-item:hover {
            background: rgba(201, 168, 118, 0.05);
            border-radius: 10px;
            padding: 15px;
            margin: 0 -15px;
        }

        .trending-item:last-child {
            border-bottom: none;
        }

        .trending-topic {
            font-weight: 600;
            color: #c9a876;
            margin-bottom: 5px;
        }

        .trending-count {
            font-size: 12px;
            color: #999;
        }

        /* Mobile Bottom Navigation */
        .mobile-nav {
            display: none;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 12px 0;
            border-top: 1px solid rgba(139, 69, 19, 0.1);
            box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }

        .mobile-nav-items {
            display: flex;
            justify-content: space-around;
            align-items: center;
            max-width: 500px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .mobile-nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-radius: 12px;
            min-width: 60px;
        }

        .mobile-nav-item:hover,
        .mobile-nav-item.active {
            background: rgba(139, 69, 19, 0.1);
            color: #8B4513;
            transform: translateY(-2px);
        }

        .mobile-nav-item .icon {
            font-size: 20px;
            margin-bottom: 4px;
        }

        .mobile-nav-item .label {
            font-size: 11px;
            font-weight: 500;
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .main-content {
                grid-template-columns: 1fr 2fr;
            }
            
            .trending {
                display: none;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
                padding-bottom: 90px;
            }

            .header {
                padding: 15px 20px;
                border-radius: 15px;
                margin-bottom: 20px;
            }

            .logo {
                font-size: 24px;
            }

            .nav-buttons {
                gap: 10px;
            }

            .nav-btn {
                padding: 8px 15px;
                font-size: 14px;
                border-radius: 20px;
            }

            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .sidebar {
                display: none;
            }

            .feed-section {
                padding: 20px;
                border-radius: 15px;
            }

            .feed-title {
                font-size: 20px;
            }

            .refresh-btn {
                padding: 10px 20px;
                font-size: 14px;
            }

            .create-post {
                padding: 20px;
                border-radius: 15px;
                margin-bottom: 20px;
            }

            .create-post-icon {
                font-size: 40px;
                margin-bottom: 12px;
            }

            .create-post h3 {
                font-size: 18px;
                margin-bottom: 8px;
            }

            .create-post p {
                font-size: 13px;
                margin-bottom: 15px;
            }

            .create-btn {
                padding: 12px 25px;
                font-size: 14px;
                border-radius: 20px;
            }

            .load-more {
                padding: 12px 30px;
                border-radius: 20px;
                margin-top: 20px;
            }

            .mobile-nav {
                display: block;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 10px;
                padding-bottom: 90px;
            }

            .header {
                padding: 12px 15px;
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .nav-buttons {
                width: 100%;
                justify-content: center;
            }

            .feed-section {
                padding: 15px;
            }

            .feed-header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
                margin-bottom: 20px;
            }

            .create-post {
                padding: 15px;
            }

            .create-post-icon {
                font-size: 36px;
            }

            .create-post h3 {
                font-size: 16px;
            }

            .create-post p {
                font-size: 12px;
            }
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .feed-section, .sidebar, .trending {
            animation: fadeIn 0.6s ease-out;
        }

        .feed-section {
            animation-delay: 0.1s;
        }

        .sidebar {
            animation-delay: 0.2s;
        }

        .trending {
            animation-delay: 0.3s;
        }

        /* Smooth scrolling */
        html {
            scroll-behavior: smooth;
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="logo">Naroop</div>
            <div class="nav-buttons">
                <button class="nav-btn primary">Log Out</button>
            </div>
        </header>

        <main class="main-content">
            <aside class="sidebar">
                <h3>Navigation</h3>
                <div class="sidebar-item active">Feed</div>
                <div class="sidebar-item">Explore</div>
                <div class="sidebar-item">Messages</div>
                <div class="sidebar-item">Profile</div>
                <div class="sidebar-item">Settings</div>
            </aside>

            <section class="feed-section">
                <div class="feed-header">
                    <h2 class="feed-title">Your Feed</h2>
                    <button class="refresh-btn">Refresh</button>
                </div>

                <div class="create-post">
                    <div class="create-post-icon">📝</div>
                    <h3>Share Your Story</h3>
                    <p>Connect with our community by sharing experiences that uplift and inspire our people</p>
                    <button class="create-btn">Create Post</button>
                </div>

                <div style="text-align: center; padding: 60px 0; color: #666;">
                    <div style="font-size: 64px; margin-bottom: 20px; opacity: 0.3;">📖</div>
                    <h3 style="margin-bottom: 10px; color: #333;">No stories yet</h3>
                    <p>Be the first to share your positive experience!</p>
                </div>

                <button class="load-more">Load More Stories</button>
            </section>

            <aside class="trending">
                <h3>Trending Topics</h3>
            </aside>
        </main>

        <!-- Mobile Navigation -->
        <nav class="mobile-nav">
            <div class="mobile-nav-items">
                <div class="mobile-nav-item active">
                    <div class="icon">🏠</div>
                    <div class="label">Feed</div>
                </div>
                <div class="mobile-nav-item">
                    <div class="icon">🔍</div>
                    <div class="label">Explore</div>
                </div>
                <div class="mobile-nav-item">
                    <div class="icon">💬</div>
                    <div class="label">Messages</div>
                </div>
                <div class="mobile-nav-item">
                    <div class="icon">👤</div>
                    <div class="label">Profile</div>
                </div>
            </div>
        </nav>
    </div>

    <script>
        // Add interactive functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Sidebar navigation
            const sidebarItems = document.querySelectorAll('.sidebar-item');
            sidebarItems.forEach(item => {
                item.addEventListener('click', function() {
                    sidebarItems.forEach(i => i.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // Mobile navigation
            const mobileNavItems = document.querySelectorAll('.mobile-nav-item');
            mobileNavItems.forEach(item => {
                item.addEventListener('click', function() {
                    mobileNavItems.forEach(i => i.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // Refresh button
            const refreshBtn = document.querySelector('.refresh-btn');
            refreshBtn.addEventListener('click', function() {
                this.style.transform = 'rotate(360deg)';
                setTimeout(() => {
                    this.style.transform = 'rotate(0deg)';
                }, 600);
            });

            // Create post button
            const createBtn = document.querySelector('.create-btn');
            createBtn.addEventListener('click', function() {
                alert('Post creation feature coming soon!');
            });

            // Load more button
            const loadMoreBtn = document.querySelector('.load-more');
            loadMoreBtn.addEventListener('click', function() {
                this.textContent = 'Loading...';
                setTimeout(() => {
                    this.textContent = 'Load More Stories';
                }, 1000);
            });

            // Logout button functionality
            const logoutBtn = document.querySelector('.nav-btn.primary');
            logoutBtn.addEventListener('click', function() {
                if (confirm('Do you want to logout?')) {
                    // Clear any stored user data
                    localStorage.removeItem('currentUser');
                    localStorage.removeItem('authToken');

                    // Clear any session storage
                    sessionStorage.clear();

                    // Redirect to landing page (root)
                    window.location.href = '/';
                }
            });

            // Trending topics
            const trendingItems = document.querySelectorAll('.trending-item');
            trendingItems.forEach(item => {
                item.addEventListener('click', function() {
                    const topic = this.querySelector('.trending-topic').textContent;
                    alert(`Exploring ${topic}...`);
                });
            });
        });
    </script>
</body>
</html>